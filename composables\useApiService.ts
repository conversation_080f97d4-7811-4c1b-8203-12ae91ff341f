/**
 * 统一的API服务层
 * 用于替代原有的server端API，直接调用外部后端服务
 */

import type {
  CreateChatCompletionRequest,
  CreateCompletionRequest,
  CreateImageRequest
} from 'openai';
import type { ChatSettingItem } from '@/types';

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  status: number;
  statusText: string;
}

export interface ModelInfo {
  id: string;
  object: string;
  owned_by: string;
  permission: any[];
}

export interface ListModelsResponse {
  data: ModelInfo[];
  object: string;
}

/**
 * API服务类
 */
export class ApiService {
  private baseUrl: string;
  private apiKey: string;
  private defaultHeaders: Record<string, string>;

  constructor(baseUrl?: string, apiKey?: string) {
    const config = useRuntimeConfig();
    this.baseUrl = baseUrl || config.public.apiBaseUrl;
    this.apiKey = apiKey || config.public.apiKey;

    this.defaultHeaders = {
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.apiKey}`
    };
  }

  /**
   * 更新API配置
   */
  updateConfig(setting: ChatSettingItem) {
    if (setting.apiKey) {
      this.apiKey = setting.apiKey;
      this.defaultHeaders['Authorization'] = `Bearer ${this.apiKey}`;
    }

    if (setting.apiHost) {
      this.baseUrl = setting.apiHost;
    }
  }

  /**
   * 获取可用模型列表
   */
  async getModels(): Promise<ApiResponse<ListModelsResponse>> {
    try {
      const config = useRuntimeConfig();
      const supportedModels = config.public.supportedModels as string[];

      // 构造模型数据
      const models: ModelInfo[] = supportedModels.map((modelId) => ({
        id: modelId,
        object: 'model',
        owned_by: 'system',
        permission: []
      }));

      const response: ListModelsResponse = {
        data: models,
        object: 'list'
      };

      return {
        data: response,
        status: 200,
        statusText: 'OK'
      };
    } catch (error: any) {
      return {
        error: error.message || 'Failed to get models',
        status: 500,
        statusText: 'Internal Server Error'
      };
    }
  }

  /**
   * 创建聊天完成
   */
  async createChatCompletion(
    request: CreateChatCompletionRequest
  ): Promise<Response> {
    const url = `${this.baseUrl}/chat/completions`;

    // 转换模型名称（如果需要）
    const body = {
      ...request,
      model: this.mapModelName(request.model)
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: this.defaultHeaders,
      // headers: {
      //   'Content-Type': 'application/json',
      //   Authorization: 'Bearer sk-cc271de81f054782b23c98ce0f9e8f0a'
      // },
      body: JSON.stringify(body)
    });

    return response;
  }

  /**
   * 创建文本完成
   */
  async createCompletion(request: CreateCompletionRequest): Promise<Response> {
    const url = `${this.baseUrl}/completions`;

    const response = await fetch(url, {
      method: 'POST',
      headers: this.defaultHeaders,
      // headers: {
      //   'Content-Type': 'application/json',
      //   Authorization: 'Bearer sk-cc271de81f054782b23c98ce0f9e8f0a'
      // },
      body: JSON.stringify(request)
    });

    return response;
  }

  /**
   * 创建图像生成
   */
  async createImage(request: CreateImageRequest): Promise<ApiResponse> {
    try {
      const url = `${this.baseUrl}/images/generations`;

      const response = await fetch(url, {
        method: 'POST',
        headers: this.defaultHeaders,
        body: JSON.stringify(request)
      });

      const data = await response.json();

      return {
        data,
        status: response.status,
        statusText: response.statusText
      };
    } catch (error: any) {
      return {
        error: error.message || 'Failed to create image',
        status: 500,
        statusText: 'Internal Server Error'
      };
    }
  }

  /**
   * 模型名称映射
   * 将标准的OpenAI模型名称映射到实际使用的模型名称
   */
  private mapModelName(model: string): string {
    const modelMap: Record<string, string> = {
      'gpt-3.5-turbo': 'qwen-plus',
      'gpt-4': 'qwen-plus',
      Syense: 'qwen-plus'
    };

    return modelMap[model] || model;
  }

  /**
   * 检查API连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.getModels();
      return response.status === 200;
    } catch {
      return false;
    }
  }
}

/**
 * 创建API服务实例的组合式函数
 */
export function useApiService(setting?: ChatSettingItem) {
  const apiService = new ApiService();

  if (setting) {
    apiService.updateConfig(setting);
  }

  return apiService;
}

/**
 * 全局API服务实例
 */
let globalApiService: ApiService | null = null;

export function getGlobalApiService(): ApiService {
  if (!globalApiService) {
    globalApiService = new ApiService();
  }
  return globalApiService;
}

/**
 * 更新全局API服务配置
 */
export function updateGlobalApiConfig(setting: ChatSettingItem) {
  const service = getGlobalApiService();
  service.updateConfig(setting);
}
