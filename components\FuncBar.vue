<template>
  <div class="pt-2 border-t">
    <div
      class="bar-btn dark:hover:text-gray-600"
      v-for="item in funcs"
      :key="item.type"
      @click="clickBtn(item.type)"
    >
      <component :is="item.icon" />
      <div>{{ $t(`FuncBar.${item.type}`) }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Plus, Setting, Bookshelf } from "@icon-park/vue-next";
import { useChatStore } from "@/stores/chat";

const store = useChatStore();
const funcs = [
  { type: "chat", icon: Plus },
  // { type: "image", icon: AddPicture },
  { type: "setting", icon: Setting },
  { type: "knowledgeBase", icon: Bookshelf },
  // { type: "github", icon: Github },
];

async function clickBtn(type: string) {
  if (type === "chat") {
    store.createChat();
    toggleSideBar();
  } 
  // else if (type === "image") {
  //   store.createImageChat();
  //   toggleSideBar();
  // } 
  else if (type === "setting") {
    store.showSetting = true;
    store.showKnowledgeBase = false;
    toggleSideBar();
  } 
  else if (type === "knowledgeBase") {
    store.showSetting = false;
    store.showKnowledgeBase = true;
    toggleSideBar();
  }
  //  else if (type === "github") {
  //   open("https://github.com/lianginx/chatgpt-nuxt", "_blank");
  // }
}
</script>

<style scoped></style>
