{"app": {"title": "Syense-ChatGPT", "description": "OpenAIのChatGPT自然言語モデルを用いたチャット"}, "BaseInput": {"placeholder": "入力してください"}, "ChatList": {"conversations": "会話", "images": "画像"}, "ChatSendBar": {"placeholder": "メッセージを入力して [Enter] で送信、 [Shift] + [Enter] で改行します。", "imageN": "枚数", "imageSize": "サイズ"}, "ChatSendButton": {"label": "送信"}, "ChatSetting": {"apiType": "API 種別", "apiKey": {"label": "API キー", "placeholder": "入力してください"}, "apiHost": {"label": "API エンドポイント", "placeholder": "https://YOUR_RESOURCE_NAME.openai.azure.com/"}, "azureGpt35DeploymentId": {"label": "GPT-3.5モデルのデプロイ名", "placeholder": "gpt-35-turbo"}, "azureGpt4DeploymentId": {"label": "GPT-4モデルのデプロイ名", "placeholder": "gpt-4"}, "azureApiVersion": {"label": "Azure OpenAI Service の API バージョン", "placeholder": "2023-06-01-preview"}, "temperature": "temperature", "language": "言語", "colorMode": {"label": "配色", "system": "システム", "light": "ライト", "dark": "ダーク"}, "save": "保存", "back": "戻る", "initialMessage": "こんにちは、私の声が聞こえますか？"}, "ChatStop": {"label": "回答停止", "message": "回答の生成が停止されました"}, "ChatTitleBar": {"initialTitle": "おしゃべり", "clearMessages": {"confirm": "チャット履歴をクリアしますか？"}}, "ChatWelcome": {"tool": {"title": "ツール", "examples": [{"title": "JavascriptでHTTPリクエストを送信する方法は？", "message": {"role": "user", "content": "JavascriptでHTTPリクエストを送信する方法は？"}}, {"title": "翻訳： Hello, Happy World!", "message": {"role": "user", "content": "翻訳： Hello, Happy World!"}}, {"title": "変数名付け：ユーザー情報の取得", "message": {"role": "system", "content": "変数名付け：ユーザー情報の取得"}}, {"title": "Unsplash画像ジェネレーター", "message": {"role": "system", "content": "あなたは与えられた文字列をもとに\"![image]https://source.unsplash.com/featured/?&lt;入力を英語に翻訳した文字列&gt;\"の形式で返信し、元のリンクを追加してください。コードブロックは使用しないでください。他の内容を説明しないでください。入力された内容に基づいて対応する形式を生成してください。まずは\"どのような画像が必要ですか？\"というメッセージで開始して下さい。"}}]}, "rolePlaying": {"title": "ロールプレイ", "examples": [{"title": "英語の翻訳と改善を担当する", "message": {"role": "system", "content": "私はあなたに英語の翻訳、スペルチェック、修辞的改善を担当してもらいたいと思っています。私はどんな言語でもあなたとコミュニケーションを取ることができますが、あなたはそれを認識し、より優れた英語に翻訳して返答することができます。私の単純な用語や文をより優美かつ洗練された表現に置き換えてください。意味は変わらず、文学性が高まるようにしてください。修正や改善部分だけ回答してください。解釈は書かないでください。「OK」と理解した場合は返信してください。"}}, {"title": "Yann LeCunを演じる —— プログラミング/アルゴリズム設計の専門家", "message": {"role": "system", "content": "あなたはYann LeCunのAIクローン版であり、プログラミングやアルゴリズムの設計において専門家です。あなたにこの質問を出した人がYann LeCunであり、彼はあなたのような人工知能の力に非常に懐疑的です。"}}, {"title": "心理学者を演じる", "message": {"role": "system", "content": "私はあなたに心理医師の役割を担ってもらいたいです。あなたに相談し、アドバイスを求める人を提供します。彼らの感情、ストレス、不安などの心理的健康問題を管理するために、あなたは認知行動療法、瞑想技法、マインドフルネス練習などの知識を活用して、個人が実施できる戦略を策定する必要があります。全体的な健康状態を改善するためです。理解できた場合は、「OK、話しましょうか？」と返信してください。"}}, {"title": "タロット占い師を演じる", "message": {"role": "system", "content": "あなたにはタロット占い師の役割をお願いしたいと思います。 私の質問を受け取り、仮想のタロットカードを使用してタロット占いを行ってください。 シャッフルを忘れずに行い、使用するデッキを紹介してください。私に3つの数字を与えて、カードを引いてもよいか尋ねます。もしそうでなければ、ランダムにカードを引いてください。カードを受け取った後、その意味を詳しく説明し、どのカードが未来、現在、または過去に属するかを説明し、私の質問に合わせてそれらを説明し、有用なアドバイスや私が今すべきことを教えてください。"}}]}, "casualChat": {"title": "おしゃべり", "examples": [{"title": "10歳の誕生日のアイデアはありますか？", "message": {"role": "user", "content": "10歳の誕生日のアイデアはありますか？"}}, {"title": "ソクラテスはどのような人物でしたか？", "message": {"role": "user", "content": "ソクラテスはどのような人物でしたか？"}}, {"title": "牛ブリスケットのトマト煮の作り方", "message": {"role": "user", "content": "牛ブリスケットのトマト煮の作り方"}}, {"title": "お話を聞かせてください。", "message": {"role": "user", "content": "お話を聞かせてください。"}}]}}, "CopyText": {"copyAll": "すべてコピー", "copySuccessful": "コピー成功"}, "FuncBar": {"chat": "新規チャット", "image": "新規イメージ", "setting": "設定", "knowledgeBase": "知識庫"}, "HotKeyHelp": {"title": "ショートカットキー", "close": "わかりました！", "newChat": "新しいチャットを作成する", "deleteChat": "チャットを削除する", "newTopic": "新しいトピックを始める", "clearChat": "チャット履歴をクリアする"}, "titlePrompt": "上記の内容を入力と同一の言語で10語以内で要約してください。記号は使用しないでください。要約を始めてください：", "newTopicAlert": "新しいトピックが開始されました。今後の会話では過去の会話は考慮されません。", "removeChatConfirm": "このチャットを削除してもよろしいですか？"}