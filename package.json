{"name": "Syense-ChatGPT", "license": "MIT", "private": true, "scripts": {"build": "nuxt generate", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "serve": "npx serve .output/public", "docker:dev": "docker-compose up"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxtjs/color-mode": "^3.3.0", "@nuxtjs/i18n": "^8.5.6", "@nuxtjs/tailwindcss": "^6.8.0", "@tailwindcss/typography": "^0.5.9", "@types/axios": "^0.14.4", "@types/node": "^18", "nuxt": "^3.13.0", "nuxt-icon": "^0.4.1", "tailwindcss": "^3.3.2", "typescript": "latest"}, "dependencies": {"@icon-park/vue-next": "^1.4.2", "@intlify/shared": "^9.2.2", "@intlify/vue-i18n-bridge": "^1.1.0", "@intlify/vue-router-bridge": "^1.1.0", "@pinia/nuxt": "^0.5.1", "dexie": "^3.2.4", "highlight.js": "^11.8.0", "hotkeys-js": "^3.10.3", "markdown-it": "^13.0.1", "moment": "^2.29.4", "openai": "^3.3.0", "pinia": "^2.1.7", "vue-i18n": "^9.2.2", "vue-i18n-routing": "^1.2.0"}, "overrides": {"vue": "latest"}}