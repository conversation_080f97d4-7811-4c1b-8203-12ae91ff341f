###
 # @Author: czf <EMAIL>
 # @Date: 2025-05-27 16:11:27
 # @LastEditors: czf <EMAIL>
 # @LastEditTime: 2025-10-09 11:02:07
 # @FilePath: \syense-chat\.env.example
 # @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
### 
###################
# Common Settings #
###################

# Use environment variables or not. `yes` or `no`
NUXT_PUBLIC_USE_ENV=yes

# The API type. `openai` or `azure`
NUXT_PUBLIC_API_TYPE=openai

# The API key used for authentication with OpenAI or Azure OpenAI Service.
NUXT_API_KEY=YOUR_API_KEY

# Higher values will make the output more random, while lower values will make it more focused and deterministic. `0.0` - `2.0`
NUXT_PUBLIC_DEFAULT_TEMPERATURE=1


#################################
# Azure OpenAI Service Settings #
#################################

# The endpoint of the Azure OpenAI Service.
NUXT_API_HOST=https://YOUR_RESOURCE_NAME.openai.azure.com

# API version of the Azure OpenAI Service.
NUXT_AZURE_API_VERSION=2023-06-01-preview

# Deployment name of the GPT-3.5 model on the Azure OpenAI Service.
NUXT_AZURE_GPT35_DEPLOYMENT_ID=

# Deployment name of the GPT-4 model on the Azure OpenAI Service.
NUXT_AZURE_GPT4_DEPLOYMENT_ID=

# The base URL of the API.
NUXT_PUBLIC_API_BASEL=http://localhost:3080