import type { ChatSettingItem, ChatSettingOption } from '@/types';
import { updateGlobalApiConfig } from '~/composables/useApiService';

const key = 'chatSetting';

export function saveSetting(setting: ChatSettingOption) {
  // 直接保存设置，不再需要服务器端加密
  // API密钥将在客户端直接使用，请确保在生产环境中通过环境变量配置
  localStorage.setItem(key, JSON.stringify({ ...setting }));

  // 更新全局API服务配置
  if (setting.apiKey || setting.apiHost) {
    updateGlobalApiConfig(setting as ChatSettingItem);
  }
}

export function loadSetting(): ChatSettingItem | undefined {
  const settingString = localStorage.getItem(key);
  if (settingString) {
    return JSON.parse(settingString);
  }
}
