export default defineNuxtConfig({
  // 静态站点生成配置
  nitro: {
    prerender: {
      routes: ['/']
    }
  },

  // 运行时配置 - 仅保留客户端需要的配置
  runtimeConfig: {
    public: {
      apiBaseUrl:
        process.env.NUXT_PUBLIC_API_BASE_URL ||
        'https://dashscope.aliyuncs.com/compatible-mode/v1',
      apiKey: process.env.NUXT_PUBLIC_API_KEY || '',
      apiType: 'openai',
      useEnv: 'yes',
      defaultTemperature: '1',
      // 支持的模型列表
      supportedModels: ['qwen-plus', 'qwen-turbo', 'gpt-3.5-turbo', 'gpt-4']
    }
  },

  vite: {
    define: {
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    }
  },

  modules: [
    '@nuxtjs/color-mode',
    '@nuxtjs/i18n',
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    'nuxt-icon'
  ],

  css: ['highlight.js/styles/dark.css'],

  i18n: {
    locales: [
      {
        code: 'zh',
        iso: 'zh-CN',
        file: 'zh.json',
        name: '简体中文'
      },
      {
        code: 'en',
        iso: 'en-US',
        file: 'en.json',
        name: 'English (US)'
      },
      {
        code: 'ja',
        iso: 'ja-JP',
        file: 'ja.json',
        name: '日本語'
      }
    ],
    langDir: 'locales',
    defaultLocale: 'en',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    }
  },

  tailwindcss: {
    config: {
      darkMode: 'class',
      content: [],
      plugins: [require('@tailwindcss/typography')]
    }
  },

  colorMode: {
    classSuffix: ''
  },

  ssr: false,

  // target: 'static', //部署静态站点 (已移除，Nuxt 3 不再支持)
  // compatibilityDate: '2025-06-12'
  devtools: { enabled: false },

  compatibilityDate: '2025-10-09'
});