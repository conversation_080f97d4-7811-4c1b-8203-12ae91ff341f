// https://nuxt.com/docs/api/configuration/nuxt-config

export default defineNuxtConfig({
  app: {
    server: {
      port: 3080 // 这里设置你想要的端口号，比如 8080
      // proxy: {
      //    '/api': {
      //      target: 'http://localhost:8989', // 目标地址
      //      changeOrigin: true, // 开启代理，在本地创建一个虚拟服务器，然后发送请求的数据，会同时会收到请求的数据，这样服务端和服务端进行数据的交互就不会有跨域问题
      //      rewrite: (path) => path.replace(/^\/api/, '') // 路径重写，移除路径中的 /api 前缀
      //    }
      //    // 可以添加更多的代理规则
      //  }
    }
  },

  runtimeConfig: {
    apiKey: 'test',
    apiHost: 'test',
    azureApiVersion: '2025-06-01-preview',
    azureGpt35DeploymentId: '',
    azureGpt4DeploymentId: '',
    public: {
      useEnv: 'no',
      apiType: 'openai',
      defaultTemperature: '1'
    }
  },

  vite: {
    define: {
      __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false
    }
  },

  modules: [
    '@nuxtjs/color-mode',
    '@nuxtjs/i18n',
    '@nuxtjs/tailwindcss',
    '@pinia/nuxt',
    'nuxt-icon'
  ],

  css: ['highlight.js/styles/dark.css'],

  i18n: {
    locales: [
      {
        code: 'zh',
        iso: 'zh-CN',
        file: 'zh.json',
        name: '简体中文'
      },
      {
        code: 'en',
        iso: 'en-US',
        file: 'en.json',
        name: 'English (US)'
      },
      {
        code: 'ja',
        iso: 'ja-JP',
        file: 'ja.json',
        name: '日本語'
      }
    ],
    langDir: 'locales',
    defaultLocale: 'en',
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root'
    }
  },

  tailwindcss: {
    config: {
      darkMode: 'class',
      content: [],
      plugins: [require('@tailwindcss/typography')]
    }
  },

  colorMode: {
    classSuffix: ''
  },

  ssr: false,

  // target: 'static', //部署静态站点 (已移除，Nuxt 3 不再支持)
  devtools: { enabled: false },

  compatibilityDate: '2025-06-12'
});
