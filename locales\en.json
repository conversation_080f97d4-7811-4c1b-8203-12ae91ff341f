{"app": {"title": "Syense-ChatGPT", "description": "AI conversations based on the ChatGPT natural language model from OpenAI"}, "BaseInput": {"placeholder": "Pleas input."}, "ChatList": {"conversations": "Conversations", "images": "Images"}, "ChatSendBar": {"placeholder": "Type a message and press [Enter] to send, press [Shift]+[Enter] to start a new line.", "imageN": "Number of images", "imageSize": "Size of image"}, "ChatSendButton": {"label": "Send"}, "ChatSetting": {"apiType": "API Type", "apiKey": {"label": "API Key", "placeholder": "Pleas input."}, "apiHost": {"label": "API Endpoint", "placeholder": "https://YOUR_RESOURCE_NAME.openai.azure.com/"}, "azureGpt35DeploymentId": {"label": "Deployment name of the GPT-3.5 model", "placeholder": "gpt-35-turbo"}, "azureGpt4DeploymentId": {"label": "Deployment name of the GPT-4 model", "placeholder": "gpt-4"}, "azureApiVersion": {"label": "API version of Azure OpenAI Service", "placeholder": "2023-06-01-preview"}, "temperature": "temperature", "language": "Language", "colorMode": {"label": "Color", "system": "System", "light": "Light", "dark": "Dark"}, "save": "Save", "back": "Back", "initialMessage": "Hey! Can you hear me?"}, "ChatStop": {"label": "Stop Generating", "message": "Generating stopped"}, "ChatTitleBar": {"initialTitle": "Talk", "clearMessages": {"confirm": "Are you sure you want to clear the chat history?"}}, "ChatWelcome": {"tool": {"title": "Tool", "examples": [{"title": "How to send HTTP requests using Javascript?", "message": {"role": "user", "content": "How to send HTTP requests using Javascript?"}}, {"title": "Translation: 你好，快乐的世界！", "message": {"role": "user", "content": "Translation: 你好，快乐的世界！"}}, {"title": "Variable Naming: Retrieving User Information.", "message": {"role": "system", "content": "Variable Naming: Retrieving User Information."}}, {"title": "Unsplash Image Generator", "message": {"role": "system", "content": "Please reply using the format of \"![image]https://source.unsplash.com/featured/?&lt;translated English content&gt;\" and append the original link. Do not use code blocks, do not describe other content, and do not explain. Generate the corresponding format based on what I enter. If you understand, please reply with \"Please tell me what image you need?\""}}]}}, "CopyText": {"copyAll": "Copy All", "copySuccessful": "Copy Successful"}, "FuncBar": {"chat": "New Chat", "image": "New Image", "setting": "Setting", "knowledgeBase": "Knowledge Base"}, "HotKeyHelp": {"title": "Global shortcut keys", "close": "Got it!", "newChat": "New Chat", "deleteChat": "Delete Chat", "newTopic": "Start a new topic", "clearChat": "Clear chat history"}, "titlePrompt": "Summarize the above content within 10 words in the same language as input, and use it as the title. Do not use symbols. Begin summarizing.", "newTopicAlert": "New topic has started, history messages will not be involved in this conversation!", "removeChatConfirm": "Are you sure you want to delete this chat?"}