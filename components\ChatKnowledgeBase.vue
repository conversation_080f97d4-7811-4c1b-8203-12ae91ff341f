<template>
  <div class="flex flex-col p-6 space-y-6 overflow-y-scroll">
    <div class="setting-item">
      <label class="setting-title">选择或新建知识库</label>
      <select v-model="setting.knowledgeBase">
        <option v-for="option in knowledgeBaseOptions" :value="option.value">{{ option.label }}</option>
      </select>
      <input type="text" placeholder="输入知识库名称" v-model.trim="setting.knowledgeBase" />
    </div>

    <!-- 文件上传区域 -->
    <div class="setting-item">
      <label class="setting-title block mb-2">上传知识文件 </label>
      <div class="border rounded p-4 mb-2 bg-gray-50 dark:bg-gray-700 relative flex items-center justify-between">
        <div class="flex items-center">
          <UploadTwo class="text-gray-500 mr-5" size="44" />
          <div class="upload-tips">
            <div class="flex items-center space-x-2">
              <span>Drag and drop files here</span>
              <input type="file" multiple @change="handleFileChange" class="hidden" ref="fileInput" />
              <!-- <button class="main-button" @click="triggerFileInput">Browse files</button> -->
            </div>
            <div class="text-xs text-gray-500 mt-2">
              Limit 200MB per file. 支持: HTML, MD, JSON, CSV, PDF, PNG, JPG, JPEG, BMP, EML, MSG, RTF, TXT, DOCX, EPUB,
              ODT,
              PPT, PPTX, TSV, HTM
            </div>
          </div>
        </div>
        <div class="flex items-center justify-end h-full">
          <button class="main-button" @click="triggerFileInput">Browse files</button>
        </div>
      </div>
    </div>

    <!-- 文件处理设置 -->
    <div class="setting-item">
      <label class="setting-title block mb-2">文件处理配置</label>
      <div class="border rounded p-4 mb-4">
        <div class="flex items-center space-x-8 mb-4">
          <!-- 单段文本最大长度 -->
          <div class="flex items-center bg-gray-50 border rounded px-3 py-2">
            <span class="mr-2">单段文本最大长度：</span>
            <input type="number" v-model.number="setting.maxLength" min="1"
              class="w-16 h-8 text-center border rounded mr-2" />
            <button class="border w-8 h-8 flex items-center justify-center px-0 rounded mr-1"
              @click="setting.maxLength = Math.max(1, setting.maxLength - 1)">-</button>
            <button class="border w-8 h-8 flex items-center justify-center px-0 rounded"
              @click="setting.maxLength = setting.maxLength + 1">+</button>
          </div>
          <!-- 相邻文本重合长度 -->
          <div class="flex items-center bg-gray-50 border rounded px-3 py-2">
            <span class="mr-2">相邻文本重合长度：</span>
            <input type="number" v-model.number="setting.overlapLength" min="0"
              class="w-16 h-8 text-center border rounded mr-2" />
            <button class="border w-8 h-8 flex items-center justify-center px-0 rounded mr-1"
              @click="setting.overlapLength = Math.max(0, setting.overlapLength - 1)">-</button>
            <button class="border w-8 h-8 flex items-center justify-center px-0 rounded"
              @click="setting.overlapLength = setting.overlapLength + 1">+</button>
          </div>

          <!-- 开启中文标题加强 -->
          <div class="flex items-center justify-center ml-4">
            <input type="checkbox" v-model="setting.titleEnhance" id="titleEnhance" class="mr-1" />
            <span for="titleEnhance" class="select-none text-sm">开启中文标题加强</span>
          </div>
        </div>
        <button class="main-button" @click="addFilesToKnowledgeBase">
          添加文件到知识库
        </button>
      </div>
    </div>

    <!-- 文件列表 -->
    <div class="setting-item">
      <label class="setting-title block mb-2">
        知识库
        <span class="mx-1 text-green-600 font-mono font-normal">{{ setting.knowledgeBase }}</span>
        中已有文件
      </label>
      <div class="bg-blue-50 text-blue-900 rounded px-4 py-2 mb-3 pt-3 pb-3">
        知识库中包含源文件与向量库，请从下表中选择文件后操作
      </div>
      <table class="min-w-full text-sm border">
        <thead>
          <tr>
            <th class="border px-2 h-10">序号</th>
            <th class="border px-2  h-10">文件名称</th>
            <th class="border px-2  h-10">文档加载器</th>
            <th class="border px-2  h-10">分词器</th>
            <th class="border px-2  h-10">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(file, idx) in fileList" :key="file.name">
            <td class="border px-2 text-center align-middle">{{ idx + 1 }}</td>
            <td class="border px-2 text-center align-middle">{{ file.name }}</td>
            <td class="border px-2 text-center align-middle">{{ file.loader }}</td>
            <td class="border px-2 text-center align-middle">{{ file.tokenizer }}</td>
            <td class="border px-2 text-center align-middle">
              <button class="second-button" @click="downloadFile(file)">下载</button>
              <button class="second-button" @click="removeFile(file)">删除</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
     <div class="flex justify-start space-x-3 mt-4">
      <button class="second-button" disabled>下载选中文件</button>
      <button class="second-button" disabled>添加至向量库</button>
      <button class="second-button" disabled>从向量库删除</button>
        <button class="main-button" @click="save">
        {{ $t("ChatSetting.save") }}
      </button>
      <button class="second-button" @click="store.showKnowledgeBase = false">
        {{ $t("ChatSetting.back") }}
      </button>
    </div>


    <!-- <div class="space-x-3">
      <button class="main-button" @click="save">
        {{ $t("ChatSetting.save") }}
      </button>
      <button class="second-button" @click="store.showKnowledgeBase = false">
        {{ $t("ChatSetting.back") }}
      </button>
    </div> -->
  </div>
</template>

<script setup lang="ts">
import type { ApiType, ChatSettingOption } from "@/types";
import { UploadTwo } from "@icon-park/vue-next";
import { useChatStore } from "~/stores/chat";

const runtimeConfig = useRuntimeConfig();
const store = useChatStore();
const i18n = useI18n();
const availableLocales = i18n.locales.value;

const knowledgeBaseOptions = ref([
  { value: "samples", label: "samples (faiss @m3e-base)" },
  // 其他知识库选项...
]);
const fileList = ref([
  { name: "test.txt", loader: "UnstructuredFileLoader", tokenizer: "ChineseRecursive" },
  { name: "langchain-ChatGLM_closed.csv", loader: "CSVLoader", tokenizer: "ChineseRecursive" },
]);

const fileInput = ref<HTMLInputElement | null>(null);


const useEnv = runtimeConfig.public.useEnv === "yes";
const setting = ref<any>({
  apiType: useEnv ? (runtimeConfig.public.apiType as ApiType) : "openai",
  apiKey: useEnv ? undefined : "",
  apiHost: useEnv ? undefined : "",
  azureGpt35DeploymentId: useEnv ? undefined : "",
  azureGpt4DeploymentId: useEnv ? undefined : "",
  azureApiVersion: useEnv ? undefined : "2023-06-01-preview",
  temperature: useEnv ? Number(runtimeConfig.public.defaultTemperature) : 1,
  locale: i18n.getBrowserLocale()!,
  colorMode: "system",
  type: "global",
});

const colorMode = useColorMode();

onMounted(() => {
  setting.value = loadSetting() ?? setting.value;
});

function triggerFileInput() {
  fileInput.value?.click();
}
function handleFileChange(e: Event) {
  // 处理文件上传逻辑
}
function addFilesToKnowledgeBase() {
  // 处理添加文件到知识库逻辑
}
function downloadFile(file: any) {
  // 下载文件逻辑
}
function removeFile(file: any) {
  // 删除文件逻辑
   // 弹出确认框，用户确认后再删除
  if (confirm(`确定要删除文件 "${file.name}" 吗？`)) {
    // 实际删除逻辑
    const idx = fileList.value.findIndex(f => f.name === file.name);
    if (idx !== -1) {
      fileList.value.splice(idx, 1);
    }
  }
}
async function save() {
  if (!useEnv && !setting.value.apiKey!.trim()) return;
  await saveSetting(setting.value);
  i18n.setLocale(store.getLocale());
  colorMode.preference = store.getColorMode();
  await store.getAvailableModels();

  store.showKnowledgeBase = false;
  store.showSetting = true;

}
</script>

<style scoped>
label:not(.radio-switch) {
  @apply block mb-2 text-sm font-medium text-gray-900 dark:text-slate-300;
}

input[type="password"],
input[type="text"],
select {
  @apply bg-gray-50 border border-gray-300 dark:border-gray-500 text-gray-900 dark:text-slate-300 dark:bg-gray-700 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5;
}

input[type="range"] {
  @apply w-full h-2 bg-gray-200 dark:bg-gray-500 rounded-lg appearance-none cursor-pointer;
}

button {
  @apply font-medium rounded-lg text-sm px-5 py-2.5 text-center;
}

.main-button {
  @apply text-white dark:text-slate-300 bg-blue-700 hover:bg-blue-800;
}

.second-button {
  @apply bg-white dark:bg-slate-400 text-gray-900 hover:bg-gray-50 dark:hover:bg-slate-300 border shadow-sm;
}

.setting-title {
  font-size: 16px !important;
  font-weight: 600 !important;
}
</style>
