# Syense-ChatGPT

[Nuxt 3](https://nuxt.com/)
[OpenAI API](https://openai.com/blog/openai-api)和[Azure Open AI Service API](https://learn.microsoft.com/zh-cn/azure/cognitive-services/openai/reference)和Syense知识库的 [ChatGPT](https://openai.com/blog/chatgpt)

[DALL·E](https://openai.com/dall-e-2)--暂未用到

## 支持的模型

- Chat completion
  - gpt-4
  - gpt-3.5-turbo
- Image generation
  - DALL·E

## 设置

```bash
Node21.5.0 + Nuxt3.13.0 + pnpm9.12.1
```

```bash
pnpm i
```

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

```bash

pnpm run build  --Node的SSR部署（需要在服务器安装node以及配置其它）
pnpm run generate  --部署静态站点（将 dist 目录部署到服务器 ， Nginx）
```

```bash
npm run preview
# or
yarn preview
# or
pnpm preview
```

## 部署

使用 Docker 部署：

```bash
docker run -d \
  -p 80:3000 \
  --restart unless-stopped \
  --name chatgpt-nuxt \
  lianginx/chatgpt-nuxt:latest
```

使用 Docker Compose 文件部署：

```bash
version: "3"
services:
  chatgpt-nuxt:
    image: lianginx/chatgpt-nuxt:latest
    ports:
      - 80:3000
    restart: unless-stopped
```

```bash
docker-compose up -d # 启动并在后台运行。
docker-compose stop  # 停止
docker-compose down  # 停止并删除
```

## 配置

如果要设置环境变量，请先参考 [`.env.example`](/.env.example) 并在根目录下创建 `.env` 文件。
