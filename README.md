# Syense-ChatGPT

[Nuxt 3](https://nuxt.com/)
[OpenAI API](https://openai.com/blog/openai-api)

## 支持的模型

- Chat completion
  - gpt-4
  - gpt-3.5-turbo

## 设置

```bash
Node21.5.0 + Nuxt3.13.0 + pnpm9.12.1
```

```bash
pnpm i
```

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

```bash

pnpm run build --部署静态站点（将 dist 目录部署到服务器 ， Nginx）
```

```bash
npm run preview
# or
yarn preview
# or
pnpm preview
```

## 部署

```bash

# 阿里云通义千问
NUXT_PUBLIC_API_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
NUXT_PUBLIC_API_KEY=your-dashscope-api-key

# OpenAI 官方
NUXT_PUBLIC_API_BASE_URL=https://api.openai.com/v1
NUXT_PUBLIC_API_KEY=sk-your-openai-api-key


NUXT_PUBLIC_API_BASE_URL=https://your-backend.com/v1
NUXT_PUBLIC_API_KEY=your-custom-api-key
```


