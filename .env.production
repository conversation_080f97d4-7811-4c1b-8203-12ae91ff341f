 
# 静态站点部署环境配置
 

###################
# API Settings    #
###################


# 阿里云通义千问
NUXT_PUBLIC_API_BASE_URL="https://dashscope.aliyuncs.com/compatible-mode/v1"
NUXT_PUBLIC_API_KEY="sk-********************************"

# OpenAI官方API
# NUXT_PUBLIC_API_BASE_URL=https://api.openai.com/v1
# NUXT_PUBLIC_API_KEY=sk-your-openai-api-key

 
# NUXT_PUBLIC_API_BASE_URL=https://your-backend-api.com/v1
# NUXT_PUBLIC_API_KEY=your-custom-api-key

# Azure OpenAI (如果使用Azure)
# NUXT_PUBLIC_API_BASE_URL=https://your-resource.openai.azure.com/openai
# NUXT_PUBLIC_API_KEY=your-azure-api-key

###################
# Build Settings  #
###################

# 构建时的基础路径 (如果部署在子目录下)
# NUXT_APP_BASE_URL=/chat/

# CDN域名 (如果使用CDN)
# NUXT_APP_CDN_URL=https://cdn.example.com