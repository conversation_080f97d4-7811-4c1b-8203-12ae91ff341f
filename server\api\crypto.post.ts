import CryptoJS from 'crypto-js';
import type { CryptoRequest } from '@/types';

const key = 'chat';

export default defineEventHandler(async (event) => {
  const crypto = (await readBody(event)) as CryptoRequest;
  return aes<PERSON>rypto(crypto);
});

export function aesCrypto(crypto: CryptoRequest) {
  if (crypto.type === 'en') {
    return CryptoJS.AES.encrypt(crypto.message, key).toString();
  } else {
    return CryptoJS.AES.decrypt(crypto.message, key).toString(
      CryptoJS.enc.Utf8
    );
  }
}
